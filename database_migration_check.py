#!/usr/bin/env python3
"""
Database Migration Readiness Check
Comprehensive analysis for MongoDB Atlas migration
"""

import os
import sys
from datetime import datetime, timedelta
from pymongo import MongoClient
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def connect_to_database():
    """Connect to MongoDB database."""
    try:
        mongo_uri = os.getenv('MONGO_URI', 'mongodb://localhost:27017/sisarasa')
        client = MongoClient(mongo_uri)
        db = client.get_default_database()
        
        # Test connection
        client.admin.command('ping')
        print(f"✅ Connected to database: {db.name}")
        return db, client
    except Exception as e:
        print(f"❌ Database connection failed: {e}")
        sys.exit(1)

def get_database_stats(db):
    """Get comprehensive database statistics."""
    print("\n🗄️  DATABASE OVERVIEW")
    print("=" * 60)
    
    # Database-level stats
    db_stats = db.command("dbStats", scale=1024)  # Scale to KB
    
    print(f"Database Name: {db_stats['db']}")
    print(f"Collections: {db_stats['collections']}")
    print(f"Views: {db_stats.get('views', 0)}")
    print(f"Total Objects: {db_stats['objects']:,}")
    print(f"Average Object Size: {db_stats['avgObjSize']:.2f} bytes")
    print(f"Data Size: {db_stats['dataSize']:.2f} KB")
    print(f"Storage Size: {db_stats['storageSize']:.2f} KB")
    print(f"Index Size: {db_stats['indexSize']:.2f} KB")
    print(f"Total Size: {db_stats['totalSize']:.2f} KB")
    print(f"Free Storage: {db_stats.get('freeStorageSize', 0):.2f} KB")
    
    return db_stats

def analyze_collections(db):
    """Analyze each collection in detail."""
    print("\n📊 COLLECTION ANALYSIS")
    print("=" * 60)
    
    collections = db.list_collection_names()
    collection_stats = {}
    
    for collection_name in collections:
        try:
            stats = db.command("collStats", collection_name, scale=1024)
            collection_stats[collection_name] = stats
            
            print(f"\n📁 {collection_name.upper()}")
            print(f"   Documents: {stats['count']:,}")
            print(f"   Size: {stats['size']:.2f} KB")
            print(f"   Storage Size: {stats['storageSize']:.2f} KB")
            print(f"   Average Document Size: {stats['avgObjSize']:.2f} bytes")
            print(f"   Indexes: {stats['nindexes']}")
            print(f"   Total Index Size: {stats['totalIndexSize']:.2f} KB")
            
        except Exception as e:
            print(f"   ❌ Error getting stats for {collection_name}: {e}")
    
    return collection_stats

def check_indexes(db):
    """Check indexes across all collections."""
    print("\n🔍 INDEX ANALYSIS")
    print("=" * 60)
    
    collections = db.list_collection_names()
    total_indexes = 0
    
    for collection_name in collections:
        try:
            indexes = list(db[collection_name].list_indexes())
            total_indexes += len(indexes)
            
            print(f"\n📁 {collection_name}:")
            for idx in indexes:
                index_name = idx.get('name', 'unnamed')
                index_keys = list(idx.get('key', {}).keys())
                print(f"   - {index_name}: {', '.join(index_keys)}")
                
        except Exception as e:
            print(f"   ❌ Error listing indexes for {collection_name}: {e}")
    
    print(f"\n📈 Total Indexes: {total_indexes}")

def analyze_data_distribution(db):
    """Analyze data distribution and patterns."""
    print("\n📈 DATA DISTRIBUTION ANALYSIS")
    print("=" * 60)
    
    # User statistics
    total_users = db.users.count_documents({})
    print(f"👥 Total Users: {total_users:,}")
    
    # Review statistics
    total_reviews = db.recipe_reviews.count_documents({})
    print(f"⭐ Total Reviews: {total_reviews:,}")
    
    if total_users > 0:
        print(f"📊 Reviews per User: {total_reviews / total_users:.1f}")
    
    # Recent activity (last 30 days)
    thirty_days_ago = datetime.utcnow() - timedelta(days=30)
    recent_users = db.users.count_documents({'created_at': {'$gte': thirty_days_ago}})
    recent_reviews = db.recipe_reviews.count_documents({'created_at': {'$gte': thirty_days_ago}})
    
    print(f"\n📅 Last 30 Days Activity:")
    print(f"   New Users: {recent_users:,}")
    print(f"   New Reviews: {recent_reviews:,}")
    
    # Rating distribution
    try:
        rating_dist = list(db.recipe_reviews.aggregate([
            {'$group': {'_id': '$rating', 'count': {'$sum': 1}}},
            {'$sort': {'_id': 1}}
        ]))
        
        print(f"\n⭐ Rating Distribution:")
        for rating in rating_dist:
            print(f"   {rating['_id']} stars: {rating['count']:,} reviews")
    except Exception as e:
        print(f"   ❌ Error getting rating distribution: {e}")

def check_migration_readiness(db_stats, collection_stats):
    """Assess migration readiness for MongoDB Atlas."""
    print("\n🚀 MONGODB ATLAS MIGRATION READINESS")
    print("=" * 60)
    
    total_size_mb = db_stats['totalSize'] / 1024  # Convert KB to MB
    data_size_mb = db_stats['dataSize'] / 1024
    
    print(f"📏 Database Size Assessment:")
    print(f"   Total Size: {total_size_mb:.2f} MB")
    print(f"   Data Size: {data_size_mb:.2f} MB")
    
    # Atlas tier recommendations
    if total_size_mb < 512:
        tier = "M0 (Free Tier)"
        cost = "Free"
    elif total_size_mb < 2048:
        tier = "M2 (Shared)"
        cost = "$9/month"
    elif total_size_mb < 10240:
        tier = "M10 (Dedicated)"
        cost = "$57/month"
    else:
        tier = "M20+ (Dedicated)"
        cost = "$134+/month"
    
    print(f"💰 Recommended Atlas Tier: {tier}")
    print(f"💵 Estimated Cost: {cost}")
    
    # Migration considerations
    print(f"\n✅ Migration Readiness Checklist:")
    
    # Check for large collections
    large_collections = []
    for name, stats in collection_stats.items():
        if stats['size'] > 100 * 1024:  # > 100MB
            large_collections.append((name, stats['size'] / 1024))
    
    if large_collections:
        print(f"⚠️  Large Collections (>100MB):")
        for name, size_mb in large_collections:
            print(f"   - {name}: {size_mb:.2f} MB")
    else:
        print(f"✅ No large collections detected")
    
    # Check document count
    total_docs = db_stats['objects']
    if total_docs > 1000000:
        print(f"⚠️  High document count: {total_docs:,} documents")
    else:
        print(f"✅ Document count manageable: {total_docs:,} documents")
    
    # Check indexes
    total_indexes = sum(stats.get('nindexes', 0) for stats in collection_stats.values())
    if total_indexes > 50:
        print(f"⚠️  Many indexes: {total_indexes} total indexes")
    else:
        print(f"✅ Index count reasonable: {total_indexes} indexes")

def identify_cleanup_candidates(db):
    """Identify collections that can be cleaned up before migration."""
    print(f"\n🧹 PRE-MIGRATION CLEANUP ANALYSIS")
    print("=" * 60)

    collections = db.list_collection_names()
    backup_collections = []
    empty_collections = []
    small_collections = []

    for collection_name in collections:
        try:
            count = db[collection_name].count_documents({})

            # Identify backup collections
            if any(keyword in collection_name.lower() for keyword in ['backup', '_bak', '_old']):
                backup_collections.append((collection_name, count))

            # Identify empty collections
            elif count == 0:
                empty_collections.append(collection_name)

            # Identify very small collections (< 10 documents)
            elif count < 10:
                small_collections.append((collection_name, count))

        except Exception as e:
            print(f"   ❌ Error analyzing {collection_name}: {e}")

    print(f"\n📦 Backup Collections Found ({len(backup_collections)}):")
    total_backup_docs = 0
    for name, count in backup_collections:
        print(f"   - {name}: {count:,} documents")
        total_backup_docs += count

    print(f"\n🗑️ Empty Collections ({len(empty_collections)}):")
    for name in empty_collections:
        print(f"   - {name}")

    print(f"\n📄 Small Collections ({len(small_collections)}):")
    for name, count in small_collections:
        print(f"   - {name}: {count} documents")

    print(f"\n💾 Space Savings Potential:")
    print(f"   Backup collections: {total_backup_docs:,} documents")
    print(f"   Empty collections: {len(empty_collections)} collections")

    return {
        'backup_collections': backup_collections,
        'empty_collections': empty_collections,
        'small_collections': small_collections
    }

def generate_migration_script(db):
    """Generate comprehensive migration commands."""
    print(f"\n📝 MIGRATION COMMANDS")
    print("=" * 60)

    print(f"# 1. Pre-migration cleanup (RECOMMENDED)")
    print(f"python pre_migration_cleanup.py")

    print(f"\n# 2. Export your database")
    print(f"mongodump --uri='mongodb://localhost:27017/sisarasa' --out=./backup")

    print(f"\n# 3. Create Atlas cluster and get connection string")
    print(f"# Visit: https://cloud.mongodb.com")

    print(f"\n# 4. Import to Atlas")
    print(f"mongorestore --uri='<ATLAS_CONNECTION_STRING>' ./backup/sisarasa")

    print(f"\n# 5. Update your application connection string")
    print(f"# Replace MONGO_URI in your .env file with Atlas connection string")

    print(f"\n# 6. Verify migration")
    print(f"python verify_migration.py")

def main():
    """Main function to run all checks."""
    print("🔍 MONGODB ATLAS MIGRATION READINESS CHECK")
    print("=" * 60)
    print(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    # Connect to database
    db, client = connect_to_database()
    
    try:
        # Get comprehensive statistics
        db_stats = get_database_stats(db)
        collection_stats = analyze_collections(db)
        check_indexes(db)
        analyze_data_distribution(db)
        check_migration_readiness(db_stats, collection_stats)
        generate_migration_script(db)
        
        print(f"\n✅ Migration readiness check completed!")
        print(f"📋 Review the analysis above to plan your Atlas migration.")
        
    except Exception as e:
        print(f"❌ Error during analysis: {e}")
    finally:
        client.close()

if __name__ == "__main__":
    main()