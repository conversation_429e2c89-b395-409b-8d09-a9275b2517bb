#!/usr/bin/env python3
"""
MongoDB Atlas Migration Script for SisaRasa System

This comprehensive script handles the complete migration process:
1. Pre-migration validation
2. Data export from local MongoDB
3. Atlas cluster verification
4. Data import to Atlas
5. Post-migration validation
6. Application configuration update
"""

import os
import sys
import json
import subprocess
import time
from datetime import datetime
from pymongo import MongoClient
from dotenv import load_dotenv
import shutil

# Load environment variables
load_dotenv()

class AtlasMigration:
    def __init__(self):
        self.local_uri = os.getenv('MONGO_URI', 'mongodb://localhost:27017/sisarasa')
        self.atlas_uri = None
        self.backup_dir = f"./migration_backup_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.migration_log = []
        
    def log(self, message, level="INFO"):
        """Log migration steps."""
        timestamp = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        log_entry = f"[{timestamp}] {level}: {message}"
        self.migration_log.append(log_entry)
        print(log_entry)
        
    def connect_to_local(self):
        """Connect to local MongoDB."""
        try:
            client = MongoClient(self.local_uri)
            db = client.get_default_database()
            client.admin.command('ping')
            self.log(f"✅ Connected to local database: {db.name}")
            return db, client
        except Exception as e:
            self.log(f"❌ Local database connection failed: {e}", "ERROR")
            return None, None
            
    def connect_to_atlas(self, atlas_uri):
        """Connect to Atlas cluster."""
        try:
            client = MongoClient(atlas_uri)
            db = client.get_default_database()
            client.admin.command('ping')
            self.log(f"✅ Connected to Atlas cluster: {db.name}")
            return db, client
        except Exception as e:
            self.log(f"❌ Atlas connection failed: {e}", "ERROR")
            return None, None
            
    def validate_local_database(self):
        """Validate local database before migration."""
        self.log("\n🔍 VALIDATING LOCAL DATABASE")
        self.log("=" * 60)
        
        db, client = self.connect_to_local()
        if not db:
            return False
            
        try:
            # Check core collections
            core_collections = ['users', 'recipes', 'recipe_reviews']
            validation_passed = True
            
            for collection_name in core_collections:
                count = db[collection_name].count_documents({})
                if count == 0:
                    self.log(f"❌ Critical collection {collection_name} is empty!", "ERROR")
                    validation_passed = False
                else:
                    self.log(f"✅ {collection_name}: {count:,} documents")
            
            # Check for backup collections
            collections = db.list_collection_names()
            backup_collections = [c for c in collections if 'backup' in c.lower()]
            
            if backup_collections:
                self.log(f"⚠️ Found {len(backup_collections)} backup collections")
                self.log("   Consider running pre_migration_cleanup.py first")
            
            client.close()
            return validation_passed
            
        except Exception as e:
            self.log(f"❌ Validation error: {e}", "ERROR")
            client.close()
            return False
            
    def export_database(self):
        """Export local database using mongodump."""
        self.log("\n📦 EXPORTING LOCAL DATABASE")
        self.log("=" * 60)
        
        try:
            # Create backup directory
            os.makedirs(self.backup_dir, exist_ok=True)
            self.log(f"📁 Created backup directory: {self.backup_dir}")
            
            # Run mongodump
            cmd = [
                'mongodump',
                '--uri', self.local_uri,
                '--out', self.backup_dir
            ]
            
            self.log(f"🔄 Running: {' '.join(cmd)}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log("✅ Database export completed successfully")
                
                # Verify backup
                db_backup_dir = os.path.join(self.backup_dir, 'sisarasa')
                if os.path.exists(db_backup_dir):
                    files = os.listdir(db_backup_dir)
                    self.log(f"📊 Exported {len(files)//2} collections")  # .bson and .metadata.json files
                    return True
                else:
                    self.log("❌ Backup directory not found", "ERROR")
                    return False
            else:
                self.log(f"❌ Export failed: {result.stderr}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ Export error: {e}", "ERROR")
            return False
            
    def get_atlas_connection(self):
        """Get Atlas connection string from user."""
        self.log("\n🌐 ATLAS CONNECTION SETUP")
        self.log("=" * 60)
        
        print("\nTo get your Atlas connection string:")
        print("1. Go to https://cloud.mongodb.com")
        print("2. Select your cluster")
        print("3. Click 'Connect' -> 'Connect your application'")
        print("4. Copy the connection string")
        print("5. Replace <password> with your actual password")
        
        while True:
            atlas_uri = input("\nEnter your Atlas connection string: ").strip()
            
            if not atlas_uri:
                print("❌ Connection string cannot be empty")
                continue
                
            if 'mongodb+srv://' not in atlas_uri:
                print("❌ Invalid Atlas connection string format")
                continue
                
            # Test connection
            self.log("🔄 Testing Atlas connection...")
            db, client = self.connect_to_atlas(atlas_uri)
            
            if db:
                client.close()
                self.atlas_uri = atlas_uri
                return True
            else:
                retry = input("❌ Connection failed. Try again? (y/N): ").lower().strip()
                if retry != 'y':
                    return False
                    
    def import_to_atlas(self):
        """Import data to Atlas using mongorestore."""
        self.log("\n📤 IMPORTING TO ATLAS")
        self.log("=" * 60)
        
        if not self.atlas_uri:
            self.log("❌ Atlas URI not set", "ERROR")
            return False
            
        try:
            # Run mongorestore
            db_backup_dir = os.path.join(self.backup_dir, 'sisarasa')
            
            cmd = [
                'mongorestore',
                '--uri', self.atlas_uri,
                '--drop',  # Drop existing collections
                db_backup_dir
            ]
            
            self.log(f"🔄 Running: mongorestore --uri=<ATLAS_URI> --drop {db_backup_dir}")
            result = subprocess.run(cmd, capture_output=True, text=True)
            
            if result.returncode == 0:
                self.log("✅ Database import completed successfully")
                return True
            else:
                self.log(f"❌ Import failed: {result.stderr}", "ERROR")
                return False
                
        except Exception as e:
            self.log(f"❌ Import error: {e}", "ERROR")
            return False
            
    def validate_atlas_data(self):
        """Validate data in Atlas after migration."""
        self.log("\n✅ VALIDATING ATLAS DATA")
        self.log("=" * 60)
        
        # Connect to both databases
        local_db, local_client = self.connect_to_local()
        atlas_db, atlas_client = self.connect_to_atlas(self.atlas_uri)
        
        if not local_db or not atlas_db:
            return False
            
        try:
            validation_passed = True
            core_collections = ['users', 'recipes', 'recipe_reviews', 'community_posts']
            
            for collection_name in core_collections:
                if collection_name in local_db.list_collection_names():
                    local_count = local_db[collection_name].count_documents({})
                    atlas_count = atlas_db[collection_name].count_documents({})
                    
                    if local_count == atlas_count:
                        self.log(f"✅ {collection_name}: {atlas_count:,} documents (matches local)")
                    else:
                        self.log(f"❌ {collection_name}: Local={local_count:,}, Atlas={atlas_count:,}", "ERROR")
                        validation_passed = False
            
            local_client.close()
            atlas_client.close()
            return validation_passed
            
        except Exception as e:
            self.log(f"❌ Validation error: {e}", "ERROR")
            if local_client:
                local_client.close()
            if atlas_client:
                atlas_client.close()
            return False
            
    def update_env_file(self):
        """Update .env file with Atlas connection string."""
        self.log("\n⚙️ UPDATING APPLICATION CONFIGURATION")
        self.log("=" * 60)
        
        try:
            # Backup current .env
            if os.path.exists('.env'):
                shutil.copy('.env', '.env.backup')
                self.log("📦 Created .env backup")
            
            # Read current .env
            env_lines = []
            if os.path.exists('.env'):
                with open('.env', 'r') as f:
                    env_lines = f.readlines()
            
            # Update MONGO_URI
            updated = False
            for i, line in enumerate(env_lines):
                if line.startswith('MONGO_URI='):
                    env_lines[i] = f"MONGO_URI={self.atlas_uri}\n"
                    updated = True
                    break
            
            if not updated:
                env_lines.append(f"MONGO_URI={self.atlas_uri}\n")
            
            # Write updated .env
            with open('.env', 'w') as f:
                f.writelines(env_lines)
            
            self.log("✅ Updated .env file with Atlas connection string")
            return True
            
        except Exception as e:
            self.log(f"❌ Failed to update .env file: {e}", "ERROR")
            return False
            
    def save_migration_log(self):
        """Save migration log to file."""
        log_file = f"migration_log_{datetime.now().strftime('%Y%m%d_%H%M%S')}.txt"
        try:
            with open(log_file, 'w') as f:
                f.write('\n'.join(self.migration_log))
            self.log(f"📝 Migration log saved: {log_file}")
        except Exception as e:
            self.log(f"❌ Failed to save log: {e}", "ERROR")
            
    def run_migration(self):
        """Run the complete migration process."""
        self.log("🚀 MONGODB ATLAS MIGRATION STARTED")
        self.log("=" * 60)
        self.log(f"Timestamp: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        try:
            # Step 1: Validate local database
            if not self.validate_local_database():
                self.log("❌ Local database validation failed. Aborting migration.", "ERROR")
                return False
            
            # Step 2: Export database
            if not self.export_database():
                self.log("❌ Database export failed. Aborting migration.", "ERROR")
                return False
            
            # Step 3: Get Atlas connection
            if not self.get_atlas_connection():
                self.log("❌ Atlas connection setup failed. Aborting migration.", "ERROR")
                return False
            
            # Step 4: Import to Atlas
            if not self.import_to_atlas():
                self.log("❌ Atlas import failed. Aborting migration.", "ERROR")
                return False
            
            # Step 5: Validate Atlas data
            if not self.validate_atlas_data():
                self.log("❌ Atlas data validation failed. Please check manually.", "ERROR")
                return False
            
            # Step 6: Update application configuration
            if not self.update_env_file():
                self.log("⚠️ Failed to update .env file. Please update manually.", "WARNING")
            
            self.log("\n🎉 MIGRATION COMPLETED SUCCESSFULLY!")
            self.log("=" * 60)
            self.log("Next steps:")
            self.log("1. Test your application with the new Atlas connection")
            self.log("2. Verify all features work correctly")
            self.log("3. Remove local backup files when satisfied")
            
            return True
            
        except Exception as e:
            self.log(f"❌ Migration failed: {e}", "ERROR")
            return False
        finally:
            self.save_migration_log()

def main():
    """Main migration function."""
    migration = AtlasMigration()
    
    print("🌟 Welcome to SisaRasa MongoDB Atlas Migration Tool")
    print("=" * 60)
    print("This tool will migrate your local MongoDB to Atlas.")
    print("Make sure you have:")
    print("1. MongoDB tools (mongodump, mongorestore) installed")
    print("2. Atlas cluster created and configured")
    print("3. Network access configured in Atlas")
    print("4. Database user created in Atlas")
    
    proceed = input("\nProceed with migration? (y/N): ").lower().strip()
    if proceed != 'y':
        print("❌ Migration cancelled by user")
        return
    
    success = migration.run_migration()
    
    if success:
        print("\n✅ Migration completed successfully!")
        print("Your SisaRasa application is now connected to MongoDB Atlas.")
    else:
        print("\n❌ Migration failed. Check the log for details.")
        print("Your local database remains unchanged.")

if __name__ == "__main__":
    main()
